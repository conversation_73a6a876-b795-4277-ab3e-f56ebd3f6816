name: inspireui
description: Common useful and Widget Use For FluxStore Products (Flutter E-Commerce App)
version: 2.2.7
homepage: https://inspireui.com/

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=1.12.0"

dependencies:
  flutter:
    sdk: flutter
  timeago: 3.7.0
  intl: any
  event_bus: ^2.0.1
  encrypt: ^5.0.3
  dio: ^5.7.0
  logger: ^2.4.0
  flutter_svg: ^2.0.13
  http: ^1.1.0
  flutter_secure_storage: ^9.2.4
  plugin_platform_interface: ^2.1.8
  easy_debounce: ^2.0.3
  universal_platform: ^1.1.0


flutter:
  uses-material-design: true

  plugin:
    platforms:
      android:
        package: com.inspireui.common_library
        pluginClass: CommonLibraryPlugin
      ios:
        pluginClass: CommonLibraryPlugin

dev_dependencies:
  flutter_lints: ^5.0.0
