#import "CommonLibraryPlugin.h"
#if __has_include(<inspireui/inspireui-Swift.h>)
#import <inspireui/inspireui-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "inspireui-Swift.h"
#endif

@implementation CommonLibraryPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftCommonLibraryPlugin registerWithRegistrar:registrar];
}
@end
