import 'package:flutter/material.dart';

import '../extensions/color_extension.dart';
import 'material_tip.dart';

part 'preview_json/jsonview.dart';

class PreviewJson extends StatelessWidget {
  final Map? data;
  final Function? onClickCopy;

  const PreviewJson({
    super.key,
    this.data,
    this.onClickCopy,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Stack(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.all(10),
            color:
                Theme.of(context).colorScheme.secondary.withValueOpacity(0.02),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  JsonView(
                    Map<String, dynamic>.from(data!),
                  )
                ],
              ),
            ),
          ),
          <PERSON><PERSON>(
            alignment: Alignment.topRight,
            child: MaterialTip(
              message: 'Copy to <PERSON>lip<PERSON>',
              child: <PERSON><PERSON><PERSON><PERSON>(
                width: 50,
                height: 50,
                child: TextButton(
                  style: TextButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.secondary,
                      padding: const EdgeInsets.symmetric(vertical: 4)),
                  onPressed: onClickCopy as void Function()?,
                  child: Icon(
                    Icons.content_copy,
                    color: Theme.of(context).primaryIconTheme.color,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
