{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_tracking_transparency", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_tracking_transparency-2.0.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "devicelocale", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.12.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_dynamic_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_direct_caller_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_direct_caller_plugin-0.0.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_ios-2.13.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mobile_ads", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/", "native_build": true, "dependencies": ["webview_flutter_wkwebview"], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "<PERSON><PERSON>", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/inspireui/", "native_build": true, "dependencies": ["flutter_secure_storage"], "dev_dependency": false}, {"name": "libphonenumber_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/libphonenumber_plugin-0.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "notification_permissions", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/notification_permissions/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/photo_manager/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "qr_code_scanner_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rate_my_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rate_my_app-2.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.3.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "the_apple_sign_in", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/the_apple_sign_in-1.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "devicelocale", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.12.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_dynamic_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-6.1.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_direct_caller_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_direct_caller_plugin-0.0.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_facebook_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth-6.1.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "native_build": true, "dependencies": [], "dev_dependency": true}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.27/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "gms_check", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/gms_check/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_android-2.14.14/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "google_mobile_ads", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/", "native_build": true, "dependencies": ["webview_flutter_android"], "dev_dependency": false}, {"name": "google_sign_in_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+22/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "in_app_update", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/in_app_update-4.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "<PERSON><PERSON>", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/inspireui/", "native_build": true, "dependencies": ["flutter_secure_storage"], "dev_dependency": false}, {"name": "libphonenumber_plugin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/libphonenumber_plugin-0.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.48/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "notification_permissions", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/notification_permissions/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/photo_manager/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "qr_code_scanner_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rate_my_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rate_my_app-2.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.3.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.15/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "devicelocale", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "facebook_auth_desktop", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/facebook_auth_desktop-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.12.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.4/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-5.4.2/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_inappwebview_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.8.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "photo_manager", "path": "/Users/<USER>/Flutter-Projects/Idea2App/idea2app_customer/packages/photo_manager/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "video_player_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.18.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "devicelocale", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.1/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.12.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "local_auth_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.5/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "devicelocale", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/devicelocale-0.7.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+10/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.1/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.21.1/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.4/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_remote_config_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.8.2/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "flutter_facebook_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_facebook_auth_web-5.0.0/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_inappwebview_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_localization", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_localization-0.2.3/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_native_splash", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/", "dependencies": [], "dev_dependency": true}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "google_maps_flutter_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_maps_flutter_web-0.5.11/", "dependencies": [], "dev_dependency": false}, {"name": "google_sign_in_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "libphonenumber_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/libphonenumber_web-0.3.2/", "dependencies": [], "dev_dependency": false}, {"name": "location_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location_web-4.2.0/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.3.2/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.0/", "dependencies": [], "dev_dependency": false}, {"name": "video_player_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.4/", "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_web-0.2.3+4/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_tracking_transparency", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "devicelocale", "dependencies": []}, {"name": "facebook_auth_desktop", "dependencies": ["flutter_secure_storage"]}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_dynamic_links", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_remote_config", "dependencies": ["firebase_core", "firebase_remote_config_web"]}, {"name": "firebase_remote_config_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_direct_caller_plugin", "dependencies": []}, {"name": "flutter_facebook_auth", "dependencies": ["flutter_facebook_auth_web", "facebook_auth_desktop"]}, {"name": "flutter_facebook_auth_web", "dependencies": []}, {"name": "flutter_inappwebview", "dependencies": ["flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "flutter_inappwebview_android", "dependencies": []}, {"name": "flutter_inappwebview_ios", "dependencies": []}, {"name": "flutter_inappwebview_macos", "dependencies": []}, {"name": "flutter_inappwebview_web", "dependencies": []}, {"name": "flutter_inappwebview_windows", "dependencies": []}, {"name": "flutter_localization", "dependencies": ["shared_preferences"]}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "gms_check", "dependencies": []}, {"name": "google_maps_flutter", "dependencies": ["google_maps_flutter_android", "google_maps_flutter_ios", "google_maps_flutter_web"]}, {"name": "google_maps_flutter_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "google_maps_flutter_ios", "dependencies": []}, {"name": "google_maps_flutter_web", "dependencies": []}, {"name": "google_mobile_ads", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview", "webview_flutter"]}, {"name": "google_sign_in", "dependencies": ["google_sign_in_android", "google_sign_in_ios", "google_sign_in_web"]}, {"name": "google_sign_in_android", "dependencies": []}, {"name": "google_sign_in_ios", "dependencies": []}, {"name": "google_sign_in_web", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "in_app_update", "dependencies": []}, {"name": "<PERSON><PERSON>", "dependencies": ["flutter_secure_storage"]}, {"name": "libphonenumber_plugin", "dependencies": ["libphonenumber_web"]}, {"name": "libphonenumber_web", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "location", "dependencies": ["location_web"]}, {"name": "location_web", "dependencies": []}, {"name": "notification_permissions", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "photo_manager", "dependencies": []}, {"name": "qr_code_scanner_plus", "dependencies": []}, {"name": "rate_my_app", "dependencies": ["shared_preferences"]}, {"name": "restart_app", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sms_autofill", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "the_apple_sign_in", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "video_player", "dependencies": ["video_player_android", "video_player_avfoundation", "video_player_web"]}, {"name": "video_player_android", "dependencies": []}, {"name": "video_player_avfoundation", "dependencies": []}, {"name": "video_player_web", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_web", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-08-28 11:30:43.011448", "version": "3.35.1", "swift_package_manager_enabled": {"ios": false, "macos": false}}