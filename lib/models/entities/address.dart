import 'package:collection/collection.dart';
import 'package:country_pickers/utils/utils.dart';

import '../../common/constants.dart';
import 'country_state.dart';

class Address {
  String? firstName;
  String? lastName;
  String? email;
  String? street;
  String? apartment;
  String? block;
  String? city;
  String? stateId;
  String? stateName;
  String? country;
  String? countryId;
  String? phoneNumber;
  String? zipCode;
  String? mapUrl;
  String? latitude;
  String? longitude;
  String? floor;
  bool isShow = true;

  Address({
    this.firstName,
    this.lastName,
    this.stateName,
    this.email,
    this.street,
    this.apartment,
    this.block,
    this.city,
    this.stateId,
    this.country,
    this.phoneNumber,
    this.zipCode,
    this.mapUrl,
    this.latitude,
    this.longitude,
    this.floor,
    this.isShow = true,
  });

  // set stateName
  void setStateName({
    required List<CountryState> states,
    required String stateId,
  }) {
    final cityName =
        states.firstWhereOrNull((element) => element.id == stateId)?.name ?? '';

    stateName = cityName;

    printLog('CITY_Name $stateName');
  }

  Address.fromJson(Map parsedJson) {
    firstName = parsedJson['first_name'] ?? '';
    lastName = parsedJson['last_name'] ?? '';
    apartment = parsedJson['company'] ?? '';
    street = parsedJson['address_1'] ?? '';
    block = parsedJson['address_2'] ?? '';
    city = parsedJson['city'] ?? '';
    stateId = parsedJson['state'] ?? '';
    country = parsedJson['country'] ?? '';
    stateName = parsedJson['state_name'] ?? '';
    email = parsedJson['email'] ?? '';
    isShow = parsedJson['isShow'] is bool ? parsedJson['isShow'] : true;
    // final alphanumeric = RegExp(r'^[a-zA-Z0-9]+$');
    // if (alphanumeric.hasMatch(firstName!)) {
    //   phoneNumber = firstName;
    // }
    phoneNumber = parsedJson['phone'] ?? '';
    floor = parsedJson['floor'] ?? '';
    zipCode = parsedJson['postcode'];
  }

  factory Address.fromStrapiJson(Map<String, dynamic> json) => Address(
      stateId: json['state'],
      city: json['city'],
      street: json['street_name'],
      block: json['building'],
      floor: json['floor'],
      apartment: json['apartment']);

  Map<String, dynamic> toJson() {
    var address = <String, dynamic>{
      'first_name': firstName,
      'last_name': lastName,
      'address_1': street ?? '',
      'address_2': block ?? '',
      'company': apartment ?? '',
      'city': city,
      'state': stateId,
      'country': country,
      'phone': phoneNumber,
      'postcode': zipCode,
      'mapUrl': mapUrl,
      'isShow': isShow,
      'state_name': stateName,
    };
    if (email != null && email!.isNotEmpty) {
      address['email'] = email;
    }
    return address;
  }

  Address.fromLocalJson(Map json) {
    try {
      firstName = json['first_name'];
      lastName = json['last_name'];
      street = json['address_1'];
      block = json['address_2'];
      apartment = json['company'];
      floor = json['floor'];
      city = json['city'];
      stateId = json['state'];
      country = json['country'];
      email = json['email'];
      phoneNumber = json['phone'];
      zipCode = json['postcode'];
      mapUrl = json['mapUrl'];
      stateName = json['state_name'];
      isShow = json['isShow'] is bool ? json['isShow'] : true;
    } catch (e) {
      printLog(e.toString());
    }
  }

  bool isValid() {
    return firstName!.isNotEmpty &&
        lastName!.isNotEmpty &&
        email!.isNotEmpty &&
        street!.isNotEmpty &&
        city!.isNotEmpty &&
        stateId!.isNotEmpty &&
        country!.isNotEmpty &&
        phoneNumber!.isNotEmpty;
  }

  Map<String, dynamic> toOrderJson() {
    var address = <String, dynamic>{
      'street_name': street ?? '',
      'building': block ?? '',
      'floor': zipCode ?? '',
      'apartment': apartment ?? '',
      'city': city,
      'state': stateName ?? stateId,
    };
    // if (email != null && email!.isNotEmpty) {
    //   address['email'] = email;
    // }
    return address;
  }

  // Address.fromLocalJson(Map json) {//TODO-addressLocalJson
  //   try {
  //     firstName = json['first_name'];
  //     lastName = json['last_name'];
  //     street = json['address_1'];
  //     block = json['address_2'];
  //     apartment = json['company'];
  //     city = json['city'];
  //     state = json['state'];
  //     floor = json['floor'];
  //     country = json['country'];
  //     email = json['email'];
  //     phoneNumber = json['phone'];
  //     zipCode = json['postcode'];
  //     mapUrl = json['mapUrl'];
  //   } catch (e) {
  //     printLog(e.toString());
  //   }
  // }

  Map<String, dynamic> toJsonEncodable() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'address_1': street ?? '',
      'address_2': block ?? '',
      'company': apartment ?? '',
      'city': city,
      'state': stateId,
      'floor': floor,
      'country': country,
      'email': email,
      'phone': phoneNumber,
      'postcode': zipCode,
      'mapUrl': mapUrl,
      'isShow': isShow,
      'state_name': stateName,
    };
  }

  @override
  String toString() {
    var output = '';
    if (street != null) {
      output += ' $street';
    }
    if (country != null) {
      output += ' $country';
    }
    if (city != null) {
      output += ' $city';
    }
    if (stateId != null) {
      output += ' ${stateName ?? stateId}';
    }
    if (zipCode != null) {
      output += ' $zipCode';
    }

    return output.trim();
  }

  String get fullName => [firstName ?? '', lastName ?? ''].join(' ').trim();

  String get fullAddress => [
        block ?? '',
        apartment ?? '',
        street ?? '',
        city ?? '',
        stateName ?? stateId ?? '',
        floor ?? '',
        zipCode ?? '',
        country ?? '',
      ].join(' ').trim();

  String get fullInfoAddress {
    var info = [];

    if (stateId?.isNotEmpty ?? false) {
      info.add(stateName ?? stateId);
    }

    if (city?.isNotEmpty ?? false) {
      info.add(city);
    }

    if (street?.isNotEmpty ?? false) {
      info.add(street);
    }

    if (country?.isNotEmpty ?? false) {
      info.add(_getCountryName(country));
    }

    if (block?.isNotEmpty ?? false) {
      info.add(block);
    }

    if (floor?.isNotEmpty ?? false) {
      info.add(floor);
    }

    //? Floor
    if (zipCode?.isNotEmpty ?? false) {
      info.add(zipCode);
    }

    if (apartment?.isNotEmpty ?? false) {
      info.add(apartment);
    }

    var address = info.join(', ');
    // if (zipCode?.isNotEmpty ?? false) {
    //   address = '$address - $zipCode';
    // }

    return address;
  }

  String get paymentAddress {
    var info = [];

    if (stateName?.isNotEmpty ?? false) {
      info.add(stateName);
    }

    return info.join(', ');
  }

  String _getCountryName(country) {
    try {
      return CountryPickerUtils.getCountryByIsoCode(country).name;
    } catch (err) {
      return country;
    }
  }

  bool isDiff(Address address) {
    return city != address.city ||
        street != address.street ||
        zipCode != address.zipCode ||
        stateId != address.stateId;
  }

  bool compareFullInfo(Address address) {
    return (city != address.city ||
            street != address.street ||
            zipCode != address.zipCode ||
            stateId != address.stateId ||
            fullName != address.fullName ||
            phoneNumber != address.phoneNumber ||
            firstName != address.firstName ||
            lastName != address.lastName) ==
        false;
  }
}
