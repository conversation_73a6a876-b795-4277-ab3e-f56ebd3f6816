// ignore_for_file: prefer_single_quotes, lines_longer_than_80_chars final

Map<String, dynamic> environment = {
  "appConfig": "lib/config/config_en.json",
  "serverConfig": {"url": "https://backend.idea2app.tech", "type": "strapi"},
  "defaultDarkTheme": false,
  "enableRemoteConfigFirebase": false,
  "enableFirebaseAnalytics": false,
  "webProxy": "",
  "loginSMSConstants": {
    "dialCodeDefault": "+1",
    "nameDefault": "United States",
    "countryCodeDefault": "US"
  },
  "phoneNumberConfig": {
    "dialCodeDefault": "+1",
    "customCountryList": [],
    "selectorFlagAsPrefixIcon": true,
    "enable": false,
    "countryCodeDefault": "US",
    "showCountryFlag": true,
    "selectorType": "BOTTOM_SHEET",
    "useInternationalFormat": true
  },
  "appRatingConfig": {
    "minLaunches": 10,
    "showOnOpen": false,
    "remindDays": 7,
    "appStoreIdentifier": "**********",
    "minDays": 7,
    "remindLaunches": 10,
    "googlePlayIdentifier": "com.idea2App.customer_app"
  },
  "advanceConfig": {
    "DefaultLanguage": "en",
    "DetailedBlogLayout": "halfSizeImageType",
    "EnablePointReward": false,
    "hideOutOfStock": false,
    "HideEmptyTags": true,
    "HideEmptyCategories": true,
    "hideEmptyRating": false,
    "EnableCart": true,
    "ShowBottomCornerCart": true,
    "EnableSkuSearch": true,
    "showStockStatus": true,
    "GridCount": 2,
    "isCaching": false,
    "kIsResizeImage": false,
    "httpCache": true,
    "Currencies": [
      {
        "symbol": "\$",
        "smallestUnitRate": 100,
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "USD",
        "currencyCode": "USD"
      },
      {
        "symbol": "₹",
        "decimalDigits": 0,
        "symbolBeforeTheNumber": true,
        "currency": "INR",
        "currencyCode": "INR"
      },
      {
        "symbol": "đ",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": false,
        "currency": "VND",
        "currencyCode": "VND"
      },
      {
        "symbol": "€",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "EUR",
        "currencyCode": "EUR"
      },
      {
        "symbol": "£",
        "smallestUnitRate": 100,
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "Pound sterling",
        "currencyCode": "GBP"
      },
      {
        "symbol": "AR\$",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "ARS",
        "currencyCode": "ARS"
      },
      {
        "symbol": "R",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "ZAR",
        "currencyCode": "ZAR"
      },
      {
        "symbol": "₱",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "PHP",
        "currencyCode": "PHP"
      },
      {
        "symbol": "Rp",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "IDR",
        "currencyCode": "IDR"
      },
      {
        "symbol": "SAR",
        "decimalDigits": 2,
        "symbolBeforeTheNumber": true,
        "currency": "Saudi Riyal",
        "currencyCode": "SAR"
      }
    ],
    "DefaultStoreViewCode": "",
    "EnableAttributesConfigurableProduct": ["color", "size"],
    "isMultiLanguages": true,
    "EnableSyncCartFromWebsite": false,
    "EnableSyncCartToWebsite": false,
    "EnableFirebase": true,
    "RatioProductImage": 1.2,
    "EnableCouponCode": true,
    "ShowCouponList": true,
    "ShowAllCoupons": true,
    "ShowExpiredCoupons": true,
    "AlwaysShowTabBar": false,
    "PrivacyPoliciesPageUrlOrId":
        "https://doc-hosting.flycricket.io/idea2app-customer-privacy-policy/************************************/privacy",
    "AboutUSPageUrl": "https://codecanyon.net/user/inspireui",
    "NewsPageUrl": "https://products.inspireui.com/",
    "FAQPageUrl": "https://products.inspireui.com/have-a-question/",
    "SupportPageUrl": "https://support.inspireui.com/",
    "DownloadPageUrl": "https://mstore.io/#download",
    "SocialConnectUrl": [
      {
        "name": "Facebook",
        "icon": "assets/icons/logins/facebook.png",
        "url": "https://www.facebook.com/inspireui"
      },
      {
        "name": "Instagram",
        "icon": "assets/icons/logins/instagram.png",
        "url": "https://www.instagram.com/inspireui9/"
      }
    ],
    "AutoDetectLanguage": true,
    "QueryRadiusDistance": 10,
    "MinQueryRadiusDistance": 1,
    "MaxQueryRadiusDistance": 10,
    "EnableWooCommerceWholesalePrices": false,
    "IsRequiredSiteSelection": true,
    "EnableDeliveryDateOnCheckout": true,
    "EnableNewSMSLogin": false,
    "EnableBottomAddToCart": true,
    "inAppWebView": false,
    "EnableWOOCSCurrencySwitcher": true,
    "enableProductBackdrop": false,
    "categoryImageMenu": true,
    "EnableDigitsMobileLogin": false,
    "EnableDigitsMobileFirebase": false,
    "EnableDigitsMobileWhatsApp": false,
    "WebViewScript": "",
    "versionCheck": {"enable": false, "iOSAppStoreCountry": "US"},
    "AjaxSearchURL": "",
    "AlwaysClearWebViewCache": false,
    "AlwaysClearWebViewCookie": false,
    "AlwaysRefreshBlog": false,
    "OrderNotesLinkSupport": false,
    "inAppUpdateForAndroid": {"enable": false, "typeUpdate": "flexible"},
    "categoryConfig": {"enableLargeCategories": false, "deepLevel": 3},
    "pinnedProductTags": [],
    "showOpeningStatus": true,
    "TimeShowToastMessage": 1500,
    "b2bKingConfig": {
      "enabled": false,
      "guestAccessRestriction": "replace_prices_quote"
    },
    "enablePWGiftCard": false,
    "cartCheckoutButtonLocation": "endTop",
    "showRequestNotification": true,
    "DefaultCurrency": {
      "symbol": "L.E",
      "smallestUnitRate": 100,
      "countryCode": "",
      "decimalDigits": 2,
      "symbolBeforeTheNumber": true,
      "currency": "EGP",
      "currencyCode": "egp"
    },
    "gdpr": {
      "confirmCaptcha": "PERMANENTLY DELETE",
      "showPrivacyPolicyFirstTime": false,
      "showDeleteAccount": true
    },
    "EnableAttributesLabelConfigurableProduct": ["color", "size"],
    "hideEmptyProductListRating": true
  },
  "defaultDrawer": {
    "logo": "assets/images/logo.png",
    "items": [
      {"show": true, "type": "home"},
      {"show": true, "type": "blog"},
      {"show": true, "type": "categories"},
      {"show": true, "type": "cart"},
      {"show": true, "type": "profile"},
      {"show": true, "type": "login"},
      {"show": true, "type": "category"}
    ]
  },
  "defaultSettings": [
    "biometrics",
    "products",
    "chat",
    "wishlist",
    "notifications",
    "language",
    "darkTheme",
    "order",
    "point",
    "rating",
    "privacy",
    "about"
  ],
  "loginSetting": {
    "facebookAppId": "***************",
    "requirePhoneNumberWhenRegister": false,
    "facebookLoginProtocolScheme": "fb***************",
    "smsLoginAsDefault": false,
    "showPhoneNumberWhenRegister": true,
    "IsRequiredLogin": false,
    "isResetPasswordSupported": false,
    "facebookClientToken": "",
    "appleLoginSetting": {
      "appleAccountTeamID": "S9RPAM8224",
      "iOSBundleId": "com.inspireui.mstore.flutter"
    }
  },
  "oneSignalKey": {"enable": false, "appID": ""},
  "onBoardingConfig": {
    "data": [
      {
        "image": "assets/images/fogg-delivery-1.png",
        "title": "Welcome to Idea2App",
        "desc": "Idea2App is on the way to serve you. "
      },
      {
        "image": "assets/images/fogg-uploading-1.png",
        "title": "Connect Surrounding World",
        "desc":
            "See all things happening around you just by a click in your phone. Fast, convenient and clean."
      },
      {
        "image": "assets/images/fogg-order-completed.png",
        "title": "Let's Get Started",
        "desc": "Waiting no more, let's see what we get!"
      }
    ],
    "autoCropImageByDesign": true,
    "isOnlyShowOnFirstTime": true,
    "version": 2,
    "enableOnBoarding": false,
    "showLanguage": true
  },
  "adConfig": {
    "ads": [
      {
        "iosId": "ca-app-pub-****************/**********",
        "waitingTimeToDisplay": 2,
        "provider": "google",
        "showOnScreens": ["home", "search"],
        "type": "banner",
        "androidId": "ca-app-pub-****************/**********"
      },
      {
        "iosId": "ca-app-pub-2101182411274198/**********",
        "provider": "google",
        "type": "banner",
        "androidId": "ca-app-pub-2101182411274198/**********"
      },
      {
        "iosId": "ca-app-pub-****************/**********",
        "waitingTimeToDisplay": 5,
        "provider": "google",
        "showOnScreens": ["profile"],
        "type": "interstitial",
        "androidId": "ca-app-pub-****************/**********"
      },
      {
        "iosId": "ca-app-pub-****************/**********",
        "provider": "google",
        "showOnScreens": ["cart"],
        "type": "reward",
        "androidId": "ca-app-pub-****************/**********"
      },
      {
        "iosId": "IMG_16_9_APP_INSTALL#***************_876131259906548",
        "provider": "facebook",
        "showOnScreens": ["home"],
        "type": "banner",
        "androidId": "IMG_16_9_APP_INSTALL#***************_489007588618919"
      },
      {
        "iosId": "***************_489092398610438",
        "provider": "facebook",
        "type": "interstitial",
        "androidId": "IMG_16_9_APP_INSTALL#***************_489092398610438"
      }
    ],
    "enable": false,
    "googleTestingId": [],
    "adMobAppIdIos": "ca-app-pub-7432665165146018~**********",
    "facebookTestingId": "",
    "adMobAppIdAndroid": "ca-app-pub-7432665165146018~**********"
  },
  "firebaseDynamicLinkConfig": {
    "iOSAppStoreId": "**********",
    "androidPackageName": "com.idea2App.customer_app",
    "isEnabled": true,
    "androidAppMinimumVersion": 1,
    "link": "https://mstore.io/",
    "uriPrefix": "https://fluxstoreinspireui.page.link",
    "shortDynamicLinkEnable": true,
    "iOSBundleId": "com.inspireui.mstore.flutter",
    "iOSAppMinimumVersion": "1.0.1"
  },
  "languagesInfo": [
    {
      "storeViewCode": "",
      "code": "en",
      "name": "English",
      "icon": "assets/images/country/gb.png",
      "text": "English"
    },
    {
      "storeViewCode": "ar",
      "code": "ar",
      "name": "Arabic",
      "icon": "assets/images/country/ar.png",
      "text": "العربية"
    }
  ],
  "paymentConfig": {
    "GuestCheckout": true,
    "EnableAddress": true,
    "DefaultCountryISOCode": "EG",
    "EnableAddressLocationNote": false,
    "SmartCOD": {"amountStop": 200, "enabled": true, "extraFee": 10},
    "EnableRefundCancel": true,
    "DefaultStateISOCode": "",
    "ShowTransactionDetails": false,
    "UpdateOrderStatus": false,
    "enableOrderDetailSuccessful": false,
    "CheckoutPageSlug": {"en": "checkout"},
    "ShowWebviewCheckoutSuccessScreen": true,
    "excludedPaymentIds": [],
    "EnableReview": true,
    "EnableShipping": true,
    "EnableCreditCard": false,
    "NativeOnePageCheckout": false,
    "EnableCustomerNote": true,
    "EnableOnePageCheckout": false,
    "EnableAlphanumericZipCode": false
  },
  "payments": {
    "expresspay_apple_pay": "assets/icons/payment/apple-pay-mark.svg",
    "tap": "assets/icons/payment/tap.png",
    "paystack": "assets/icons/payment/paystack.png",
    "stripe_v2_google_pay": "assets/icons/payment/google-pay-mark.png",
    "ppcp-gateway": "assets/icons/payment/paypal.svg",
    "midtrans": "assets/icons/payment/midtrans.png",
    "xendit_cc": "assets/icons/payment/xendit.png",
    "stripe_v2_apple_pay": "assets/icons/payment/apple-pay-mark.svg",
    "myfatoorah_v2": "assets/icons/payment/myfatoorah.png",
    "thai-promptpay-easy": "assets/icons/payment/prompt-pay.png",
    "stripe": "assets/icons/payment/stripe.svg",
    "razorpay": "assets/icons/payment/razorpay.svg",
    "paypal": "assets/icons/payment/paypal.svg"
  },
  "payTmConfig": {
    "merchantId": "your-merchant-id",
    "production": false,
    "paymentMethodId": "paytm",
    "enabled": true
  },
  "inAppPurchaseConfig": {
    "subscriptionProductIDs": ["com.idea2App.customer_app.subscription.test"],
    "nonConsumableProductIDs": [],
    "consumableProductIDs": ["com.idea2App.customer_app.test"],
    "enabled": false
  },
  "expressPayConfig": {
    "merchantPassword": "4a00a5fd3c63dd2b743c75746af6ffe2",
    "merchantId": "merchant.com.inspireui.mstore.flutter",
    "production": false,
    "paymentMethodId": "shahbandrpay",
    "merchantKey": "b2be2ffc-c8b9-11ed-82a9-42eb4e39c8ae",
    "enabled": true
  },
  "defaultCountryShipping": [
    {"emoji": "🇪🇬", "iosCode": "EG", "name": "Egypt"}
  ],
  "googleApiKey": {
    "web": "AIzaSyDSNYVC-8DU9BTcyqkeN9c5pgVhwOBAvGg",
    "android": "AIzaSyDSNYVC-8DU9BTcyqkeN9c5pgVhwOBAvGg",
    "ios": "AIzaSyDSNYVC-8DU9BTcyqkeN9c5pgVhwOBAvGg"
  },
  "productCard": {"defaultImage": "assets/images/no_product_image.png"},
  "productDetail": {
    "height": 0.6,
    "marginTop": 0.0,
    "safeArea": false,
    "buyButtonStyle": "normal",
    "showVideo": true,
    "showThumbnailAtLeast": 1,
    "layout": "simpleType",
    "borderRadius": 3.0,
    "ShowSelectedImageVariant": true,
    "ForceWhiteBackground": false,
    "attributeImagesSize": 50.0,
    "showSku": true,
    "showStockStatus": true,
    "showStockQuantity": true,
    "showRating": true,
    "showProductCategories": true,
    "showProductTags": true,
    "hideInvalidAttributes": true,
    "ShowImageGallery": true,
    "autoPlayGallery": false,
    "allowMultiple": false,
    "showQuantityInList": false,
    "showAddToCartInSearchResult": true,
    "productListItemHeight": 125.0,
    "limitDayBooking": 14,
    "boxFit": "cover",
    "SliderShowGoBackButton": true,
    "SliderIndicatorType": "number",
    "productMetaDataKey": "",
    "showRelatedProduct": true,
    "showRecentProduct": true,
    "productImageLayout": "page",
    "expandBrands": true,
    "expandDescription": true,
    "expandInfors": true,
    "expandCategories": true,
    "expandTags": true,
    "expandReviews": true,
    "alwaysShowBuyButton": true
  },
  "blogDetail": {
    "showAuthorInfo": true,
    "showTextAdjustment": true,
    "showRelatedBlog": true,
    "showComment": true,
    "showHeart": true,
    "showSharing": true,
    "enableAudioSupport": false
  },
  "productVariantLayout": {
    "color": "color",
    "size": "box",
    "color-image": "image",
    "height": "option"
  },
  "productAddons": {
    "allowedCustomType": ["png", "pdf", "docx"],
    "allowMultiple": false,
    "allowImageType": true,
    "allowVideoType": true,
    "allowCustomType": true,
    "fileUploadSizeLimit": 5
  },
  "cartDetail": {
    "maxAllowQuantity": 20,
    "minAllowTotalCartValue": 0,
    "style": "normal"
  },
  "productVariantLanguage": {
    "ar": {
      "color": "اللون",
      "size": "بحجم",
      "color-image": "اللون",
      "height": "ارتفاع"
    },
    "vi": {
      "color": "Màu",
      "size": "Kích thước",
      "color-image": "Màu",
      "height": "Chiều Cao"
    },
    "en": {
      "color": "Color",
      "size": "Size",
      "color-image": "Color",
      "height": "Height"
    }
  },
  "excludedCategory": "311",
  "saleOffProduct": {
    "ShowCountDown": true,
    "HideEmptySaleOffLayout": false,
    "Color": "#C7222B"
  },
  "notStrictVisibleVariant": true,
  "configChat": {
    "showOnScreens": ["profile"],
    "hideOnScreens": [],
    "EnableSmartChat": true,
    "UseRealtimeChat": false,
    "version": "2"
  },
  "openAIConfig": {
    "supabaseAnonKey":
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ0a3JxdnRzbHVqZHpqeGhqb2N1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2NzU5OTI5MzMsImV4cCI6MTk5MTU2ODkzM30.qKtfNHhL6AKqGsmDfjMq90bIWIWlnN3UVgnwcLF_vGY",
    "revenueProductsIos": ["gpt_3999_1y_1w0", "gpt_399_1m_1w0"],
    "enableSubscription": false,
    "enableInputKey": false,
    "supabaseUrl": "https://rtkrqvtslujdzjxhjocu.supabase.co",
    "revenueAppleApiKey": "appl_XNtOUZPHwUzelbvwdSezFsMrNeT",
    "revenueProductsAndroid": ["gpt_pro_v1"],
    "enableChat": false,
    "revenueGoogleApiKey": "goog_kpDTQdItiHkSrdjDdvLIwAdjOzG"
  },
  "smartChat": [
    {
      "app": "firebase",
      "imageData":
          "https://trello.com/1/cards/611a38c89ebde41ec7cf10e2/attachments/611a392cceb1b534aa92a83e/previews/611a392dceb1b534aa92a84d/download",
      "description": "Realtime Chat"
    },
    {
      "app": "chatGPT",
      "imageData": "https://i.imgur.com/pp1qlPd.png",
      "description": "Chat GPT"
    },
    {
      "app": "https://wa.me/*********",
      "description": "WhatsApp",
      "iconData": "whatsapp"
    },
    {"app": "tel:8499999999", "description": "Call Us", "iconData": "phone"},
    {"app": "sms://8499999999", "description": "Send SMS", "iconData": "sms"},
    {
      "app": "https://tawk.to/chat/5d830419c22bdd393bb69888/default",
      "description": "Tawk Chat",
      "iconData": "whatsapp"
    },
    {
      "app": "http://m.me/inspireui",
      "description": "Facebook Chat",
      "iconData": "facebookMessenger"
    },
    {
      "app":
          "https://twitter.com/messages/compose?recipient_id=821597032011931648",
      "imageData":
          "https://trello.com/1/cards/611a38c89ebde41ec7cf10e2/attachments/611a38d026894f10dc1091c8/previews/611a38d126894f10dc1091d6/download",
      "description": "Twitter Chat"
    }
  ],
  "adminEmail": "<EMAIL>",
  "adminName": "InspireUI",
  "deliveryConfig": {
    "appLogo": "assets/images/app_icon_transparent.png",
    "appName": "FluxStore Delivery",
    "dashboardName2": "Delivery",
    "dashboardName1": "FluxStore",
    "enableSystemNotes": false
  },
  "managerConfig": {
    "appLogo": "assets/images/app_icon_transparent.png",
    "appName": "FluxStore Admin",
    "enableDeliveryFeature": false
  },
  "loadingIcon": {
    "layout": "spinkit",
    "size": 35.**************,
    "type": "fadingCircle"
  },
  "splashScreen": {
    "duration": 3000,
    "image": "assets/images/app_icon.png",
    "animationName": "Idea2App",
    "backgroundColor": "#ffffff",
    "paddingBottom": 0.0,
    "enable": true,
    "paddingRight": 0.0,
    "boxFit": "contain",
    "paddingTop": 0.0,
    "type": "fade-in",
    "paddingLeft": 0.0
  },
  "orderConfig": {"version": 1},
  "darkConfig": {
    "MainColor": "ff67b0ff",
    "logo":
        "https://trello.com/1/cards/6635335b5f9777750ef2f70b/attachments/66a18d1d5b0691e9ba935c75/download/text.png",
    "backgroundColor": "ff0e110f",
    "saleColor": "#E15241"
  },
  "addressFields": [
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 1,
      "type": "firstName",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 2,
      "type": "lastName",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 3,
      "type": "phoneNumber",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 4,
      "type": "email",
      "required": true
    },
    {"visible": true, "position": 5, "type": "selectAddress"},
    {"visible": false, "position": 6, "type": "country"},
    {"visible": true, "position": 7, "type": "state"},
    {"visible": false, "position": 8, "type": "searchAddress"},
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 9,
      "type": "city",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 10,
      "type": "street",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 11,
      "type": "block",
      "required": false
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 12,
      "type": "zipCode",
      "required": true
    },
    {
      "visible": true,
      "editable": true,
      "defaultValue": "",
      "position": 13,
      "type": "apartment",
      "required": false
    }
  ],
  "lightConfig": {
    "MainColor": "ff67b0ff",
    "logo":
        "https://trello.com/1/cards/6635335b5f9777750ef2f70b/attachments/66a18d21e87bb16f911fa102/download/black_text.png",
    "saleColor": "ff000000"
  }
};
