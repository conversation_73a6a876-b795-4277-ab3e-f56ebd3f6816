part of '../constants.dart';

/// check if the environment is web
final bool kIsWeb = UniversalPlatform.isWeb;
final bool isIos = UniversalPlatform.isIOS;
final bool isAndroid = UniversalPlatform.isAndroid;
final bool isMacOS = UniversalPlatform.isMacOS;
final bool isWindow = UniversalPlatform.isWindows;
final bool isFuchsia = UniversalPlatform.isFuchsia;
final bool isMobile = UniversalPlatform.isIOS || UniversalPlatform.isAndroid;
final bool isDesktop = UniversalPlatform.isMacOS || UniversalPlatform.isWindows;

const appleType = 'apple';
const androidType = 'android';
const desktopType = 'desktop';

String getSmartPhoneOrTablet() {
  final userAgent = html.window.navigator.userAgent.toString().toLowerCase();
  // smartphone
  if (userAgent.contains('iphone')) return appleType;
  if (userAgent.contains('android')) return androidType;

  // tablet
  if (userAgent.contains('ipad')) return appleType;
  if ((html.window.navigator.platform?.toLowerCase().contains('macintel') ??
          false) &&
      (html.window.navigator.maxTouchPoints ?? 0) > 0) return appleType;

  return desktopType;
}

bool get isDesktopBrowser {
  return getSmartPhoneOrTablet() == desktopType;
}

/// constant for Magento payment
const kMagentoPayments = [
  'HyperPay_Amex',
  'HyperPay_ApplePay',
  'HyperPay_Mada',
  'HyperPay_Master',
  'HyperPay_PayPal',
  'HyperPay_SadadNcb',
  'HyperPay_Visa',
  'HyperPay_SadadPayware'
];

/// The result is limited to 10 items (https://tppr.me/QYsm9) if `options` or `modifiers` is used in `include` field. Refer to this link https://developer.bigcommerce.com/api-reference/4101d472a814d-get-all-products. So we need to override apiPageSize to 10 instead
final apiPageSize = ServerConfig().isBigCommerce ? 10 : 20;

///-----FLUXSTORE LISTING-----///
enum BookStatus { booked, unavailable, waiting, confirmed, cancelled, error }

const kSizeLeftMenu = 250.0;

class SettingConstants {
  static const aboutUsUrl = 'https://codecanyon.net/user/inspireui';
}

class SplashScreenTypeConstants {
  static const fadeIn = 'fade-in';
  static const zoomIn = 'zoom-in';
  static const zoomOut = 'zoom-out';
  static const topDown = 'top-down';
  static const rive = 'rive';
  static const flare = 'flare';
  static const static = 'static';
  static const lottie = 'lottie';
}

/// FluxNews
//Legit roles to access post management at Setting screens.
const addPostAccessibleRoles = ['author', 'administrator'];

const kEmptyCategoryID = '-1';
const kRootCategoryID = '0';

const isrgRootX1 = '''-----BEGIN CERTIFICATE-----
MIIFazCCA1OgAwIBAgIRAIIQz7DSQONZRGPgu2OCiwAwDQYJKoZIhvcNAQELBQAw
TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh
cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMTUwNjA0MTEwNDM4
WhcNMzUwNjA0MTEwNDM4WjBPMQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJu
ZXQgU2VjdXJpdHkgUmVzZWFyY2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBY
MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAK3oJHP0FDfzm54rVygc
h77ct984kIxuPOZXoHj3dcKi/vVqbvYATyjb3miGbESTtrFj/RQSa78f0uoxmyF+
0TM8ukj13Xnfs7j/EvEhmkvBioZxaUpmZmyPfjxwv60pIgbz5MDmgK7iS4+3mX6U
A5/TR5d8mUgjU+g4rk8Kb4Mu0UlXjIB0ttov0DiNewNwIRt18jA8+o+u3dpjq+sW
T8KOEUt+zwvo/7V3LvSye0rgTBIlDHCNAymg4VMk7BPZ7hm/ELNKjD+Jo2FR3qyH
B5T0Y3HsLuJvW5iB4YlcNHlsdu87kGJ55tukmi8mxdAQ4Q7e2RCOFvu396j3x+UC
B5iPNgiV5+I3lg02dZ77DnKxHZu8A/lJBdiB3QW0KtZB6awBdpUKD9jf1b0SHzUv
KBds0pjBqAlkd25HN7rOrFleaJ1/ctaJxQZBKT5ZPt0m9STJEadao0xAH0ahmbWn
OlFuhjuefXKnEgV4We0+UXgVCwOPjdAvBbI+e0ocS3MFEvzG6uBQE3xDk3SzynTn
jh8BCNAw1FtxNrQHusEwMFxIt4I7mKZ9YIqioymCzLq9gwQbooMDQaHWBfEbwrbw
qHyGO0aoSCqI3Haadr8faqU9GY/rOPNk3sgrDQoo//fb4hVC1CLQJ13hef4Y53CI
rU7m2Ys6xt0nUW7/vGT1M0NPAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV
HRMBAf8EBTADAQH/MB0GA1UdDgQWBBR5tFnme7bl5AFzgAiIyBpY9umbbjANBgkq
hkiG9w0BAQsFAAOCAgEAVR9YqbyyqFDQDLHYGmkgJykIrGF1XIpu+ILlaS/V9lZL
ubhzEFnTIZd+50xx+7LSYK05qAvqFyFWhfFQDlnrzuBZ6brJFe+GnY+EgPbk6ZGQ
3BebYhtF8GaV0nxvwuo77x/Py9auJ/GpsMiu/X1+mvoiBOv/2X/qkSsisRcOj/KK
NFtY2PwByVS5uCbMiogziUwthDyC3+6WVwW6LLv3xLfHTjuCvjHIInNzktHCgKQ5
ORAzI4JMPJ+GslWYHb4phowim57iaztXOoJwTdwJx4nLCgdNbOhdjsnvzqvHu7Ur
TkXWStAmzOVyyghqpZXjFaH3pO3JLF+l+/+sKAIuvtd7u+Nxe5AW0wdeRlN8NwdC
jNPElpzVmbUq4JUagEiuTDkHzsxHpFKVK7q4+63SM1N95R1NbdWhscdCb+ZAJzVc
oyi3B43njTOQ5yOf+1CceWxG1bQVs5ZufpsMljq4Ui0/1lvh+wjChP4kqKOJ2qxq
4RgqsahDYVvTH9w7jXbyLeiNdd8XM2w9U/t7y0Ff/9yi0GE44Za4rF2LN9d11TPA
mRGunUHBcnWEvgJBQl9nJEiU0Zsnvgc/ubhPgXRR4Xq37Z0j4r7g1SgEEzwxA57d
emyPxgcYxn/eR44/KJ4EBs+lVDR3veyJm+kXQ99b21/+jh5Xos1AnX5iItreGCc=
-----END CERTIFICATE-----
''';

/// Short video support for shopify
const kProductVideoSupportTypes = ['mp4'];

/// Appbar default values
const kAppbarShowOnScreens = [RouteList.home];

/// Tab Setting Profile
const kItemProfileTypeDefault = SettingItemStyle.listTile;

/// Limit to fecth blog
const kLimitFetchPageBlog = 15;

/// Feature scroll to top currently only support home screen
final kTabSupportScrollToTop = [RouteList.home];
